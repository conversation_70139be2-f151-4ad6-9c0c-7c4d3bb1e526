<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>LLVM Coroutine Header</Name>
  <Location>\Engine\Source\Runtime\Core\Public\Experimental\Coroutine\</Location>
  <Function>Implementation of the coroutine_handle and will be distributed with C++20.</Function>
  <Eula>https://llvm.org/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>\\UE5\Main\Engine\Source\ThirdParty\Licenses</LicenseFolder>
</TpsData>