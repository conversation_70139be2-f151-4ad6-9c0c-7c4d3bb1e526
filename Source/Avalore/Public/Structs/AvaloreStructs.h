// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "NiagaraSystem.h"
#include "Structs/AvaloreEnums.h"
#include "Styling/SlateColor.h"

#include "AvaloreStructs.generated.h"

USTRUCT(BlueprintType)
struct FHitDetails
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsCrit = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EHitSource HitSource = EHitSource::Generic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EHitImpact HitImpact = EHitImpact::Default;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EHitDirection HitDirection = EHitDirection::Front;
};

USTRUCT(BlueprintType)
struct FItemData
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ItemID = 0;

	UPROPERTY(EditA<PERSON>, BlueprintReadWrite)
	EItemType ItemType = EItemType::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FText ItemName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FText ItemDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTexture2D* ItemIcon = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName ItemAttachmentSocket;
};

USTRUCT(BlueprintType)
struct FWeaponData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EWeaponMode WeaponMode = EWeaponMode::Melee;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EWeaponType WeaponType = EWeaponType::None;
};

USTRUCT(BlueprintType)
struct FAttributeImpactTargetData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAttributeImpactTarget ImpactTarget = EAttributeImpactTarget::Resource;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EResource Resource = EResource::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EStat Stat = EStat::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float VPA = 0.0f;
};

USTRUCT(BlueprintType)
struct FAttribute
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAttribute Attribute = EAttribute::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAttributeImpactTargetData> ImpactTargetsData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<int32> Components = {0, 0, 0}; // Attribute's Components are always 3 (Base, Allocated, Other)
};

USTRUCT(BlueprintType)
struct FResource
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EResource Resource = EResource::None;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<float> Components = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f}; // Resource's Components are always 5 (Base, Attributed, Itemized, Other, Current)
};

USTRUCT(BlueprintType)
struct FStat
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EStat Stat = EStat::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<float> Components = {0.0f, 0.0f, 0.0f, 0.0f}; // Stat's Components are always 4 (Base, Attributed, Itemized, Other)
};

USTRUCT(BlueprintType)
struct FFormatResources : public FTableRowBase
{
	GENERATED_BODY()

	FFormatResources()
	{
		Health.Resource = EResource::Health;
		Mana.Resource = EResource::Mana;
		Stamina.Resource = EResource::Stamina;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FResource Health;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FResource Mana;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FResource Stamina;
};

USTRUCT(BlueprintType)
struct FFormatStats : public FTableRowBase
{
	GENERATED_BODY()

	// Default constructor to initialize each FStat's Stat field to its corresponding EStat value
	FFormatStats()
	{
		HealthRegeneration.Stat = EStat::HealthRegeneration;
		ManaRegeneration.Stat = EStat::ManaRegeneration;
		StaminaRegeneration.Stat = EStat::StaminaRegeneration;
		AttackValue.Stat = EStat::AttackValue;
		AbilityPower.Stat = EStat::AbilityPower;
		AttackSpeed.Stat = EStat::AttackSpeed;
		Armor.Stat = EStat::Armor;
		Spellguard.Stat = EStat::Spellguard;
		MovementSpeed.Stat = EStat::MovementSpeed;
		AttackRange.Stat = EStat::AttackRange;
		AbilityHaste.Stat = EStat::AbilityHaste;
		Leech.Stat = EStat::Leech;
		CriticalChance.Stat = EStat::CriticalChance;
		CriticalDamage.Stat = EStat::CriticalDamage;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat HealthRegeneration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat ManaRegeneration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat StaminaRegeneration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat AttackValue;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat AbilityPower;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat AttackSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat Armor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat Spellguard;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat MovementSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat AttackRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat AbilityHaste;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat Leech;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat CriticalChance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FStat CriticalDamage;
};

USTRUCT(BlueprintType)
struct FStartingAttributes : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Vitality = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Intelligence = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Strength = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Agility = 0;
};

USTRUCT(BlueprintType)
struct FCameraSettings
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float ZoomPerScroll = 20.0f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float MinZoom = 200.0f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float MaxZoom = 1000.0f;
};

USTRUCT(BlueprintType)
struct FFormatPlayerLevel
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Level = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 MaxLevel = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 XP = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 LevelUpXP = 0;
};

USTRUCT(BlueprintType)
struct FFormatPlayerReputation
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Points = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EPlayerReputation Reputation = EPlayerReputation::Neutral;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FLinearColor Color = FLinearColor::White;
};

USTRUCT(BlueprintType)
struct FPlayerInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FText Nickname;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatPlayerLevel Level;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatPlayerReputation Reputation;
};

USTRUCT(BlueprintType)
struct FFormatIcons
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTexture2D* NoBorder = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTexture2D* WithBorder = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTexture2D* NoBorderTransparent = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTexture2D* WithBorderTransparent = nullptr;
};

USTRUCT(BlueprintType)
struct FFormatNameplateColors : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EEntityType Entity = EEntityType::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FSlateColor Color;
};

USTRUCT(BlueprintType)
struct FFormatGeneralTiedAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Dig = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* PickUp = nullptr;
};

USTRUCT(BlueprintType)
struct FFormatWeaponTiedAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Idle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Walk;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Run;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimMontage*> Attack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta=(DisplayName = "Damage From Front"))
	TArray<UAnimMontage*> DamageFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Damage From Back"))
	TArray<UAnimMontage*> DamageFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Front"))
	TArray<UAnimMontage*> KnockFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Back"))
	TArray<UAnimMontage*> KnockFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Front"))
	TArray<UAnimMontage*> KnockRecoveryFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Back"))
	TArray<UAnimMontage*> KnockRecoveryFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Front"))
	TArray<UAnimMontage*> DeathFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Back"))
	TArray<UAnimMontage*> DeathFromBack;
};

USTRUCT(BlueprintType)
struct FFormatGroupedWeaponTiedAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatWeaponTiedAnimations Unarmed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "One Handed Sword"))
	FFormatWeaponTiedAnimations OneHandedSword;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Two Handed Sword"))
	FFormatWeaponTiedAnimations TwoHandedSword;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatWeaponTiedAnimations Bell;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatWeaponTiedAnimations Fan;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatWeaponTiedAnimations Daggers;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatWeaponTiedAnimations Bow;
};

USTRUCT(BlueprintType)
struct FPlayerCharacterAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatGeneralTiedAnimations General;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatGroupedWeaponTiedAnimations WeaponTied;
};

// Deprecated
USTRUCT(BlueprintType)
struct FFormatAnimationsWeaponTiedDeprecated : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimSequence* Idle = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimSequence* Walk = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimSequence* Run = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimMontage*> Attack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta=(DisplayName = "Damage From Front"))
	TArray<UAnimMontage*> DamageFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Damage From Back"))
	TArray<UAnimMontage*> DamageFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Front"))
	TArray<UAnimMontage*> KnockFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Back"))
	TArray<UAnimMontage*> KnockFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Front"))
	TArray<UAnimMontage*> KnockRecoveryFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Back"))
	TArray<UAnimMontage*> KnockRecoveryFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Front"))
	TArray<UAnimMontage*> DeathFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Back"))
	TArray<UAnimMontage*> DeathFromBack;
};

// Deprecated
USTRUCT(BlueprintType)
struct FFormatAnimationsGroupedWeaponTiedDeprecated : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsWeaponTiedDeprecated Unarmed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "One Handed Sword"))
	FFormatAnimationsWeaponTiedDeprecated OneHandedSword;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Two Handed Sword"))
	FFormatAnimationsWeaponTiedDeprecated TwoHandedSword;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsWeaponTiedDeprecated Bell;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsWeaponTiedDeprecated Fan;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsWeaponTiedDeprecated Daggers;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsWeaponTiedDeprecated Bow;
};

// Deprecated
USTRUCT(BlueprintType)
struct FAnimationsPlayerCharacterDeprecated : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatGeneralTiedAnimations General;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FFormatAnimationsGroupedWeaponTiedDeprecated WeaponTied;
};

USTRUCT(BlueprintType)
struct FNPCAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Idle;
};

USTRUCT(BlueprintType)
struct FMonsterAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Idle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimSequence* Walk = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimSequence* Run = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimMontage*> Attack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Damage From Front"))
	TArray<UAnimMontage*> DamageFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Damage From Back"))
	TArray<UAnimMontage*> DamageFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Front"))
	TArray<UAnimMontage*> KnockFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock From Back"))
	TArray<UAnimMontage*> KnockFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Front"))
	TArray<UAnimMontage*> KnockRecoveryFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Knock Recovery From Back"))
	TArray<UAnimMontage*> KnockRecoveryFromBack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Front"))
	TArray<UAnimMontage*> DeathFromFront;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Death From Back"))
	TArray<UAnimMontage*> DeathFromBack;
};

USTRUCT(BlueprintType)
struct FMetinstoneAnimationsDeprecated : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Spawn = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Attack = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Death = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Loop = nullptr;
};

USTRUCT(BlueprintType)
struct FMetinstoneAnimations : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimSequence*> Idle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Spawn = nullptr;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* Death = nullptr;
};

USTRUCT(BlueprintType)
struct FClickToMoveData
{
	GENERATED_BODY()
	
	// This is the maximal distance player can click in the game world to start pathing towards it
	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly)
	float MaxClickRange = 20000.0f;

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly)
	float ClickToMovePathPointReachTolerance = 100.0f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	UParticleSystem* ClickToMoveParticleSystem = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	UNiagaraSystem* TargetHighlightHoveredNiagaraSystem = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	UNiagaraSystem* TargetHighlightSelectedNiagaraSystem = nullptr;
};

USTRUCT(BlueprintType)
struct FCoreEntityData
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FName InternalFullName; // The full internal name of the entity (used for searching in the data tables)

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly)
	EEntityType EntityType = EEntityType::None; // The type of entity (PlayerCharacter, NPC, Monster, Metinstone...)
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	EEntityGender Gender = EEntityGender::None;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float InteractionRange = 250.0f; // The range within which the entity can be interacted with (this won't be used in case cases like when PlayerCharacter's attack range should be used instead)
};

USTRUCT(BlueprintType)
struct FPlayerCharacterData : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FName Placeholder;
};

USTRUCT(BlueprintType)
struct FNPCData : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FName Placeholder;
};

USTRUCT(BlueprintType)
struct FMonsterData : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FName Placeholder;
};

USTRUCT(Blueprintable)
struct FMetinstoneData : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FName Placeholder;
};