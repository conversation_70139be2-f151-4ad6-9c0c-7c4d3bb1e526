// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "ActorComponents/AttributesComponent.h"

#include "ActorComponents/StatsComponent.h"
#include "ActorComponents/ResourcesComponent.h"

#include "Entities/BaseEntity.h"
#include "Entities/Playercharacters/PlayerCharacter.h"

#include "Net/UnrealNetwork.h"
#include "Utils/DebugUtils.h"


UAttributesComponent::UAttributesComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	PopulateAttributesArray(); // Populate the Attributes array with default attribute entries
}


void UAttributesComponent::BeginPlay()
{
	Super::BeginPlay();
	
	OwningEntity = Cast<ABaseEntity>(GetOwner());

	// Cache the PlayerCharacter reference if the owner is a PlayerCharacter
	// This allows the Component to work with any BaseEntity while still supporting PlayerCharacter-specific functionality, such as the 'InitializeAttributesBaseValuesWithStartingValues()'
	OwningPlayerCharacter = Cast<APlayerCharacter>(GetOwner());

	// Initialize the Base Values of Attributes with the Starting Values as defined in the DataTable only on the Server
	// Other entities (NPCs, Metinstones, Monsters) don't really have any 'Starting Values'
	if (GetOwner()->HasAuthority() && OwningPlayerCharacter)
	{
		InitializeAttributesBaseValuesWithStartingValues();
	}
}


void UAttributesComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UAttributesComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UAttributesComponent, Attributes); // Replicates the Attributes variable to all clients
	DOREPLIFETIME(UAttributesComponent, AvailableAllocationPoints); // Replicates the AvailableAllocationPoints variable to all clients
}

void UAttributesComponent::OnRep_Attributes()
{
	OnAttributeUpdated.Broadcast(); // Broadcast the fact that an attribute has been updated so that any listeners can react to the change
}

/**
 * Populates the `Attributes` array with default attribute entries corresponding to the defined `EAttribute` enums,
 * excluding the "None" value.
 *
 * This function allocates space in the array for all valid attribute types and initializes each element by
 * associating it with a specific attribute from the `EAttribute` enumeration. The function follows the structure
 * of the enumeration to ensure that the `Attributes` array accurately represents all available attributes for the owning entity.
 *
 * @details
 * - The function dynamically determines the number of attributes based on the `EAttribute` enum values.
 * - Space is reserved in the array before elements are added to improve performance during initialization.
 * - Attributes added include: Vitality, Intelligence, Strength, and Agility, as defined in the `EAttribute` enum.
 *
 * @remarks
 * - This function is intended to be called as part of the component's setup or initialization process.
 * - The `Attributes` array must be empty or cleared prior to calling this function to avoid duplicate entries.
 * - After execution, the `Attributes` array will contain one entry for each valid attribute type, with their
 *   identifiers set according to the enum definitions.
 *
 * @warning
 * - Ensure that any modifications to the `EAttribute` enumeration are consistently reflected in this function to avoid mismatches.
 * - The function assumes the `EAttribute` enum's first value (`None`) is invalid for actual attribute entries and skips it.
 */
void UAttributesComponent::PopulateAttributesArray()
{
	Attributes.Reserve(StaticEnum<EAttribute>()->NumEnums() - 1); // Reserve space for all Enum Values (EAttribute) except 'None'
	
	Attributes.Add(FAttribute{EAttribute::Vitality});
	Attributes.Add(FAttribute{EAttribute::Intelligence});
	Attributes.Add(FAttribute{EAttribute::Strength});
	Attributes.Add(FAttribute{EAttribute::Agility});

	Attributes[0].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Resource, EResource::Health, EStat::None, 25.0f});
	Attributes[0].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::HealthRegeneration, 1.0f});

	Attributes[1].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Resource, EResource::Mana, EStat::None, 15.0f});
	Attributes[1].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::ManaRegeneration, 0.5f});

	Attributes[2].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::AttackValue, 3.0f});

	Attributes[3].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Resource, EResource::Stamina, EStat::None, 5.0f});
	Attributes[3].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::StaminaRegeneration, 0.3f});
	Attributes[3].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::MovementSpeed, 1.0f});
	Attributes[3].ImpactTargetsData.Add(FAttributeImpactTargetData{EAttributeImpactTarget::Stat, EResource::None, EStat::AttackSpeed, 0.03f});
}

/**
 * Initializes the base values of attributes with the starting values as defined in a data table.
 *
 * This function retrieves starting attribute values based on the owning player character's variant
 * and assigns these values to the respective base components of the attributes (e.g., Vitality, Intelligence, Strength, Agility).
 * It validates that the required data table and owning player character are available, and performs error logging if any
 * validation fails.
 *
 * @note
 * - The function converts the player character's variant enum to a row name for accessing the data table.
 * - If the required row is missing from the data table or invalid, an error is logged and no changes are made.
 * - Successful initialization logs confirmation with the relevant variant information.
 *
 * @remarks
 * - This function is intended to be called during the setup phase of the player character to ensure proper initialization.
 * - The `SetAttributeValue` method is used to update the base component of each attribute, propagating any necessary recalculations or updates.
 * - Proper initialization ensures attributes are set correctly based on the defined starting values for the player's variant.
 *
 * @warning
 * - Ensure `StartingAttributesDataTable` is properly populated and linked before calling this function.
 * - The owning player character must have a valid variant value, or initialization will not proceed.
 */
void UAttributesComponent::InitializeAttributesBaseValuesWithStartingValues()
{
	// This function is specifically designed for PlayerCharacters, so we need to validate that the OwningPlayerCharacter is valid
	if (!OwningPlayerCharacter)
	{
		AVALORE_ERROR_LOG(TEXT("InitializeAttributesBaseValuesWithStartingValues called on non-PlayerCharacter entity!"));
		return;
	}
	
	// Validate StartingAttributesDataTable
	if (!StartingAttributesDataTable)
	{
		AVALORE_ERROR_LOG(TEXT("StartingAttributesDataTable is null! Cannot initialize attribute Base Values!"));
		return;
	}

	// Convert Enum Variant to FName to use it as a row name for the DataTable
	FString VariantString = UEnum::GetValueAsString(OwningPlayerCharacter->Variant);
	FName RowName = FName(*VariantString);

	// Find the row in the DataTable
	FStartingAttributes* StartingAttributesRow = StartingAttributesDataTable->FindRow<FStartingAttributes>(RowName, TEXT("InitializeAttributesBaseValuesWithStartingValues"));
	if (!StartingAttributesRow)
	{
		AVALORE_ERROR_LOG(TEXT("Failed to find starting attributes row for Variant: %s"), *VariantString);
		return;
	}

	// Set the Base Values for each Attribute
	SetAttributeValue(EAttribute::Vitality, EAttributeComponent::Base, StartingAttributesRow->Vitality);
	SetAttributeValue(EAttribute::Intelligence, EAttributeComponent::Base, StartingAttributesRow->Intelligence);
	SetAttributeValue(EAttribute::Strength, EAttributeComponent::Base, StartingAttributesRow->Strength);
	SetAttributeValue(EAttribute::Agility, EAttributeComponent::Base, StartingAttributesRow->Agility);

	AVALORE_STANDARD_LOG(TEXT("Successfully initialize Base Attribute values for PlayerCharacter Variant %s"), *VariantString);
}

/**
 * Sets the value of a specific component of an attribute.
 *
 * This function modifies the value of one of the components (e.g., Base, Allocated, or Other)
 * of a given attribute, triggers any related updates, and ensures all necessary validations
 * and recalculations are performed. It is only executed on the authority (server) side and
 * updates are replicated to clients.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose component value is being set.
 * @param Component The specific component of the attribute to modify (e.g., Base, Allocated, Other).
 * @param Value The new value to set for the specified attribute's component.
 *
 * @note
 * - The function checks for authority before making any changes to the attribute.
 * - All indices and types are validated to prevent crashes or unexpected behavior due to
 *   invalid array accesses or type mismatches.
 * - Changes to attributes automatically trigger replication to clients, and the `OnRep_Attributes`
 *   function is called on the clients upon receiving the update.
 *
 * @remarks
 * - This function calls `RecalculateAttribution()` to recalculate stats and resources affected by
 *   the updated attribute value. For example, a change in the Vitality attribute may require recalculating
 *   the maximum health of a player.
 * - The `OnAttributeUpdated` event is broadcast to signal any systems or UI components
 *   that might rely on attribute changes.
 */
void UAttributesComponent::SetAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Convert the Enum to an Index
	// The Attributes array is indexed by the EAttribute enum (e.g., Vitality = 0, Strength = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 AttributeIndex = static_cast<int32>(Attribute);
	
	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Attributes array. This prevents crashes from accessing invalid memory locations.
	if (!Attributes.IsValidIndex(AttributeIndex))
		return;

	// Verify Attribute Type Consistency
	// Double-check that the attribute stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Attributes[AttributeIndex].Attribute != Attribute)
		return;

	// Convert Component Enum to Index
	// Each Attribute has 3 components: Base (0), Allocated (1), Other (2). Convert the EAttributeComponent enum to an array index.
	int32 ComponentIndex = static_cast<int32>(Component);

	// Validate Component Array Bounds
	// Ensure the component index is valid within the Components array (should be 0-2). This prevents accessing invalid component data.
	if (!Attributes[AttributeIndex].Components.IsValidIndex(ComponentIndex))
		return;

	// Update the Attribute Component Value
	// Actually set the new value in the specific component of the attribute. This is the core operation that modifies the player's Attribute data.
	Attributes[AttributeIndex].Components[ComponentIndex] = Value;

	// Broadcast the Attribute Update Event
	// Notify other components or systems that an attribute has been updated. This could trigger UI updates, stat recalculations, etc.
	OnAttributeUpdated.Broadcast();

	// Trigger Recalculation of Stats Attribution
	// Stats are Resources are effected by Attributes values, so we need to recalculate the attribution of all stats when an attribute's value changes
	// For example, Vitality affects Health, so when Vitality changes, we need to recalculate the Health stat
	RecalculateAttribution();

	// Note that Attributes array is marked as Replicated, so this change will automatically be sent to all Clients from the Server.
	// When Clients receive it, the OnRep_Attributes() function will be called, which will broadcast the OnAttributeUpdated event.
}

/**
 * Retrieves the value of a specified attribute component or the full attribute's total value.
 *
 * This function retrieves the value of an attribute's specific component, such as Base, Allocated, or Other.
 * Alternatively, if the total value of the attribute (sum of all its components) is required, it can bypass
 * individual component extraction based on the 'bIgnoreComponentAndReturnTotal' parameter.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose value is being retrieved.
 * @param Component The specific component of the attribute (e.g., Base, Allocated, or Other) to retrieve.
 * @param bIgnoreComponentAndReturnTotal If true, returns the total value of the attribute (sum of all components)
 * instead of a specific component value.
 *
 * @return The value of the requested attribute component, or the total value of the attribute if
 * 'bIgnoreComponentAndReturnTotal' is true. Returns 0 if the input parameters are invalid or out of bounds.
 */
int32 UAttributesComponent::GetAttributeValue(EAttribute Attribute, EAttributeComponent Component, bool bIgnoreComponentAndReturnTotal) const
{
	// Convert Enum to Array Index
	// The Attributes array is indexed by the EAttribute enum (e.g., Vitality = 0, Strength = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 AttributeIndex = static_cast<int32>(Attribute);

	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Attributes array. This prevents crashes from accessing invalid memory locations.
	if (!Attributes.IsValidIndex(AttributeIndex))
		return 0;

	// Verify Attribute Type Consistency
	// Double-check that the attribute stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Attributes[AttributeIndex].Attribute != Attribute)
		return 0;

	// Check if we should return total value instead of specific component only
	// If 'bIgnoreComponentAndReturnTotal' is true, we should return the total value of the attribute (sum of all its components, aka 'Base' + 'Allocated' + 'Other')
	if (bIgnoreComponentAndReturnTotal)
	{
		// Calculate total by summing all three components
		int32 Total = 0;
		for (int32 ComponentValue : Attributes[AttributeIndex].Components)
		{
			Total += ComponentValue;
		}
		return Total;
	}

	// Return Specific Component Value
	// Convert the EAttributeComponent Enum to an array index
	// Each Attribute has 3 components: Base (0), Allocated (1), Other (2).
	int32 ComponentIndex = static_cast<int32>(Component);
	if (!Attributes[AttributeIndex].Components.IsValidIndex(ComponentIndex))
		return 0; // Return 0 as safe fallback for invalid component

	// Return ther Requested Component Value
	// Return the specific component value of the attribute (Base, Allocated, or Other)
	return Attributes[AttributeIndex].Components[ComponentIndex];
}


void UAttributesComponent::ServerSetAttributeValue_Implementation(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	SetAttributeValue(Attribute, Component, Value);
}

/**
 * Increases the value of a specific component of an attribute.
 *
 * This function retrieves the current value of the specified attribute's component,
 * adds the provided increase value, and then updates the attribute's component value
 * accordingly while ensuring all necessary validations, recalculations, and replications are performed.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose component value is to be increased.
 * @param Component The specific component of the attribute to modify (e.g., Base, Allocated, Other).
 * @param Value The amount to increase the value of the specified attribute's component.
 *
 * @note
 * - Authority checks are performed to ensure this function executes only on the server.
 * - Changes to the attribute's component value automatically trigger replication to clients,
 *   along with necessary dependent updates such as recalculation of related derived stats.
 * - `GetAttributeValue` and `SetAttributeValue` functions are internally used to retrieve
 *   and update the values of the specified attribute's component.
 *
 * @remarks
 * - Using this method ensures proper validation and broadcasting of changes through the built-in attribute system.
 * - Dependent systems are notified of attribute changes through appropriate replication mechanisms and events.
 */
void UAttributesComponent::IncreaseAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get Current Component Value
	// First, retrieve the current value of the specific attribute component. We use false for bIgnoreComponentAndReturnTotal to get the specific component value only
	int32 CurrentValue = GetAttributeValue(Attribute, Component, false);

	// Calculate New Value
	// Add the increase amount to the current value to get the new total
	int32 NewValue = CurrentValue + Value;

	// Set the New Value
	// Use SetAttributeValue() to apply the new total value, which handles all validation, broadcasting, and replication
	SetAttributeValue(Attribute, Component, NewValue);
}

void UAttributesComponent::ServerIncreaseAttributeValue_Implementation(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	IncreaseAttributeValue(Attribute, Component, Value);
}

/**
 * Recalculates the attribution of all attributes to their respective stats and resources.
 *
 * This function iterates through all attributes in the component and applies their effects
 * to the corresponding stats and resources based on each attribute's impact target data.
 * The attribution system allows attributes to dynamically affect multiple stats and resources
 * with configurable multipliers (VPA - Value Per Attribute point).
 *
 * The function performs the following operations:
 * 1. Loops through each attribute in the Attributes array
 * 2. Calculates the total value of each attribute (sum of Base + Allocated + Other components)
 * 3. For each attribute, processes all its impact targets defined in ImpactTargetsData
 * 4. Calculates the attributed value using the formula: AttributeTotal * VPA
 * 5. Updates the 'Attributed' component of the target stat or resource with the calculated value
 *
 * @note
 * - This function only executes on the server (authority check) to ensure network consistency.
 * - The function requires valid StatsComponent and ResourcesComponent references from the owning entity.
 * - All calculations affect only the 'Attributed' component of stats and resources, leaving
 *   other components (Base, Itemized, Other) unchanged.
 * - The VPA (Value Per Attribute point) multiplier allows for flexible scaling of attribute effects.
 *
 * @remarks
 * - This function is typically called when attribute values change to ensure dependent stats
 *   and resources reflect the updated attribute values.
 * - The impact target system supports both EAttributeImpactTarget::Resource and 
 *   EAttributeImpactTarget::Stat, allowing attributes to affect either type of target.
 * - Each attribute can have multiple impact targets, enabling complex attribute interactions
 *   (e.g., Vitality affecting both Health stat and Health resource).
 *
 * @warning
 * - Ensure that ImpactTargetsData is properly configured in the attribute setup, as invalid
 *   enum values in the switch statement will be ignored without error logging.
 * - The function assumes that all referenced stats and resources exist in their respective components.
 *
 * Example:
 * If Vitality has a total value of 50 and an impact target with VPA of 10.0 targeting Health resource,
 * the Health resource's Attributed component will be set to 500.0 (50 * 10.0).
 */
void UAttributesComponent::RecalculateAttribution()
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get references to the StatsComponent and ResourcesComponent from the OwningEntity
	// These components are required to apply the calculated attribution values
	UStatsComponent* StatsComponent = OwningEntity->StatsComponent;
	UResourcesComponent* ResourcesComponent = OwningEntity->ResourcesComponent;

	// Validate StatsComponent and ResourcesComponent exist
	// If either component is missing, we cannot proceed with attribution
	if (!StatsComponent || !ResourcesComponent)
		return;

	// Iterate through each attribute in the Attributes array
	// Each Attribute can potentially affect multiple stats and/or resources
	for (const FAttribute& Attribute : Attributes)
	{
		// Calculate the total value of the current Attribute by summing all its components (Base, Allocated, Other)
		// Use use 'true' for bIgnoreComponentAndReturnTotal to get the total value of the Attribute
		int32 AttributeTotalValue = GetAttributeValue(Attribute.Attribute, EAttributeComponent::Other, true);

		// Process each Impact Target defined for the current Attribute
		// ImpactTargetsData contains the target stat/resource and the VPA (Value Per Attribute point) multiplier
		for (const FAttributeImpactTargetData& ImpactTargetData : Attribute.ImpactTargetsData)
		{
			// Calculate the final attributed value by using the VPA multiplier
			// Formula: AttributeTotalValue * VPA = Final Attributed Value
			// Example: Vitality (total 50) with VPA of 10.0 targeting Health resource results in 500.0 added to Health's Attributed component
			float CalculatedValue = AttributeTotalValue * ImpactTargetData.VPA;

			// Determine whether this impact target affects a Resource or a Stat and apply the calculated value to the 'Attributed' component
			switch (ImpactTargetData.ImpactTarget)
			{
			case EAttributeImpactTarget::Resource:
				// Apply the calculated value to the 'Attributed' component of the target 'Resource'
				// This affects resources like Health, Stamina, etc.
				ResourcesComponent->SetResourceValue(ImpactTargetData.Resource, EResourceComponent::Attributed, CalculatedValue);
				break;

			case EAttributeImpactTarget::Stat:
				// Apply the calculated value to the 'Attributed' component of the target 'Stat'
				// This affects stats like Attack Speed, Armor, etc.
				StatsComponent->SetStatValue(ImpactTargetData.Stat, EStatComponent::Attributed, CalculatedValue);
				break;
			}
		}
	}
}



